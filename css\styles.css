/*
  Promz Static Website Styles
  Main stylesheet for the Promz marketing website
*/

/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Base Variables - Standardized Color Palette */
:root {
  /* Primary Brand Colors */
  --primary-color: #4a6cf7;
  --primary-dark: #3959d9;
  --primary-light: #8fa4f3;
  --primary-surface: #e8f0ff;

  /* Secondary Colors */
  --secondary-color: #03dac6;
  --secondary-dark: #018786;
  --secondary-light: #a7ffeb;

  /* Semantic Colors */
  --success-color: #4caf50;
  --success-light: #e8f5e9;
  --danger-color: #f44336;
  --danger-light: #ffebee;
  --warning-color: #ff9800;
  --warning-light: #fff8e1;
  --info-color: #2196f3;
  --info-light: #e3f2fd;

  /* Neutral Colors */
  --white: #ffffff;
  --light-color: #f8f9fa;
  --light-gray: #e0e0e0;
  --gray: #9e9e9e;
  --dark-gray: #616161;
  --dark-color: #212529;

  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #9e9e9e;
  --text-on-primary: #ffffff;

  /* Background Colors */
  --body-color: #fafafa;
  --surface-color: #ffffff;
  --background-elevated: #f5f5f5;

  /* Border Colors */
  --border-color: #e0e0e0;
  --border-light: #f0f0f0;
  --border-dark: #bdbdbd;
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  
  --border-radius: 0.375rem;
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
  background-color: var(--body-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

p {
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-relaxed);
}

.lead {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
}

.small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.text-xs {
  font-size: var(--font-size-xs);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}

img {
  max-width: 100%;
  height: auto;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Header */
header {
  background-color: var(--surface-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-sm) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo img {
  height: 40px;
}

nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

nav ul li {
  margin-left: var(--spacing-md);
}

nav ul li a {
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  padding: 0.5rem;
  transition: color 0.3s ease;
  text-decoration: none;
}

nav ul li a:hover,
nav ul li a.active {
  color: var(--primary-color);
}

/* Hero Section */
.hero {
  padding: var(--spacing-xl) 0;
  background-color: var(--primary-color);
  color: white;
  text-align: center;
}

.hero h1, .hero h2 {
  color: var(--text-on-primary);
}

.hero h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
  letter-spacing: -0.05em;
}

.hero h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-snug);
  margin-bottom: var(--spacing-md);
  opacity: 0.9;
}

.hero p {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.95;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  text-align: center;
  text-decoration: none;
  letter-spacing: 0.025em;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn.primary {
  background-color: var(--white);
  color: var(--primary-color);
}

.btn.primary:hover {
  background-color: var(--light-color);
  transform: translateY(-2px);
}

.btn.secondary {
  background-color: transparent;
  color: var(--text-on-primary);
  border: 2px solid var(--white);
}

.btn.secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Features Section */
.features {
  padding: var(--spacing-xl) 0;
  background-color: var(--surface-color);
}

.features h2 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.feature-card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

/* Download Section */
.download {
  padding: var(--spacing-xl) 0;
  background-color: var(--light-color);
  text-align: center;
}

.download h2 {
  margin-bottom: var(--spacing-xs);
}

.download p {
  margin-bottom: var(--spacing-lg);
}

.download-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
}

.download-button {
  display: flex;
  align-items: center;
  background-color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 250px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.download-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.platform-icon {
  font-size: 2rem;
  margin-right: var(--spacing-sm);
}

.platform-info {
  text-align: left;
}

.platform-info h3 {
  margin-bottom: 0.25rem;
}

.platform-info p {
  margin-bottom: 0;
  color: var(--secondary-color);
  font-size: 0.875rem;
}

/* Page Header */
.page-header {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-lg) 0;
  text-align: center;
}

.page-header h1 {
  color: white;
  margin-bottom: 0.5rem;
}

.page-header p {
  margin-bottom: 0;
  opacity: 0.9;
}

/* About Content */
.about-content, .legal-content {
  padding: var(--spacing-xl) 0;
  background-color: white;
}

.about-section, .legal-section {
  margin-bottom: var(--spacing-xl);
}

.about-section h2, .legal-section h2 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.about-section ul, .legal-section ul {
  margin-bottom: var(--spacing-md);
}

.legal-section h3 {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

/* Footer */
footer {
  background-color: var(--dark-color);
  color: white;
  padding: var(--spacing-xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.footer-logo img {
  height: 40px;
  margin-bottom: var(--spacing-sm);
}

.footer-logo p {
  opacity: 0.7;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.footer-links h3, .footer-contact h3 {
  color: white;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  margin-bottom: var(--spacing-xs);
}

.footer-links ul li a {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.footer-links ul li a:hover {
  color: white;
}

.footer-contact p {
  margin-bottom: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.7);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
}

.social-icon {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.social-icon:hover {
  color: white;
}

/* Responsive Typography */
@media (max-width: 768px) {
  h1 {
    font-size: var(--font-size-3xl);
  }

  h2 {
    font-size: var(--font-size-2xl);
  }

  h3 {
    font-size: var(--font-size-xl);
  }

  .hero h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
  }

  .hero h2 {
    font-size: var(--font-size-xl);
  }

  .hero p {
    font-size: var(--font-size-lg);
  }
  
  .cta-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  nav ul li {
    margin: 0 0.5rem;
  }
  
  .download-options {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 576px) {
  header .container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .hero h1 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
  }

  .hero h2 {
    font-size: var(--font-size-lg);
  }

  .hero p {
    font-size: var(--font-size-base);
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
}