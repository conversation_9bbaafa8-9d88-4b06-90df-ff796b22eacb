/*
  Promz Static Website Styles
  Main stylesheet for the Promz marketing website
*/

/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Base Variables - Standardized Color Palette */
:root {
  /* Primary Brand Colors */
  --primary-color: #4a6cf7;
  --primary-dark: #3959d9;
  --primary-light: #8fa4f3;
  --primary-surface: #e8f0ff;

  /* Secondary Colors */
  --secondary-color: #03dac6;
  --secondary-dark: #018786;
  --secondary-light: #a7ffeb;

  /* Semantic Colors */
  --success-color: #4caf50;
  --success-light: #e8f5e9;
  --danger-color: #f44336;
  --danger-light: #ffebee;
  --warning-color: #ff9800;
  --warning-light: #fff8e1;
  --info-color: #2196f3;
  --info-light: #e3f2fd;

  /* Neutral Colors */
  --white: #ffffff;
  --light-color: #f8f9fa;
  --light-gray: #e0e0e0;
  --gray: #9e9e9e;
  --dark-gray: #616161;
  --dark-color: #212529;

  /* Text Colors */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #9e9e9e;
  --text-on-primary: #ffffff;

  /* Background Colors */
  --body-color: #fafafa;
  --surface-color: #ffffff;
  --background-elevated: #f5f5f5;

  /* Border Colors */
  --border-color: #e0e0e0;
  --border-light: #f0f0f0;
  --border-dark: #bdbdbd;
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  
  --border-radius: 0.375rem;
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
  background-color: var(--body-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

p {
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-relaxed);
}

.lead {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
}

.small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.text-xs {
  font-size: var(--font-size-xs);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}

img {
  max-width: 100%;
  height: auto;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Header */
header {
  background-color: var(--surface-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-sm) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo img {
  height: 40px;
}

nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

nav ul li {
  margin-left: var(--spacing-md);
}

nav ul li a {
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  padding: 0.5rem;
  transition: color 0.3s ease;
  text-decoration: none;
}

nav ul li a:hover,
nav ul li a.active {
  color: var(--primary-color);
}

/* Hero Section */
.hero {
  padding: var(--spacing-xl) 0;
  background-color: var(--primary-color);
  color: white;
  text-align: center;
}

.hero h1, .hero h2 {
  color: var(--text-on-primary);
}

.hero h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
  letter-spacing: -0.05em;
}

.hero h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-snug);
  margin-bottom: var(--spacing-md);
  opacity: 0.9;
}

.hero p {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.95;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  text-align: center;
  text-decoration: none;
  letter-spacing: 0.025em;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn.primary {
  background-color: var(--white);
  color: var(--primary-color);
}

.btn.primary:hover {
  background-color: var(--light-color);
  transform: translateY(-2px);
}

.btn.secondary {
  background-color: transparent;
  color: var(--text-on-primary);
  border: 2px solid var(--white);
}

.btn.secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Features Section */
.features {
  padding: var(--spacing-xl) 0;
  background-color: var(--surface-color);
}

.features h2 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.feature-card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

/* Download Section */
.download {
  padding: var(--spacing-xl) 0;
  background-color: var(--light-color);
  text-align: center;
}

.download h2 {
  margin-bottom: var(--spacing-xs);
}

.download p {
  margin-bottom: var(--spacing-lg);
}

.download-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
}

.download-button {
  display: flex;
  align-items: center;
  background-color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 250px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.download-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.platform-icon {
  font-size: 2rem;
  margin-right: var(--spacing-sm);
}

.platform-info {
  text-align: left;
}

.platform-info h3 {
  margin-bottom: 0.25rem;
}

.platform-info p {
  margin-bottom: 0;
  color: var(--secondary-color);
  font-size: 0.875rem;
}

/* Page Header */
.page-header {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-lg) 0;
  text-align: center;
}

.page-header h1 {
  color: white;
  margin-bottom: 0.5rem;
}

.page-header p {
  margin-bottom: 0;
  opacity: 0.9;
}

/* About Content */
.about-content, .legal-content {
  padding: var(--spacing-xl) 0;
  background-color: white;
}

.about-section, .legal-section {
  margin-bottom: var(--spacing-xl);
}

.about-section h2, .legal-section h2 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.about-section ul, .legal-section ul {
  margin-bottom: var(--spacing-md);
}

.legal-section h3 {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

/* Footer */
footer {
  background-color: var(--dark-color);
  color: white;
  padding: var(--spacing-xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.footer-logo img {
  height: 40px;
  margin-bottom: var(--spacing-sm);
}

.footer-logo p {
  opacity: 0.7;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.footer-links h3, .footer-contact h3 {
  color: white;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  margin-bottom: var(--spacing-xs);
}

.footer-links ul li a {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.footer-links ul li a:hover {
  color: white;
}

.footer-contact p {
  margin-bottom: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.7);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
}

.social-icon {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.social-icon:hover {
  color: white;
}

/* Download Page Styles */
.download-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: var(--light-color);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.download-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  margin: 2rem 0;
}

.download-button {
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  border-radius: 8px;
  text-decoration: none;
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  letter-spacing: 0.025em;
  transition: all 0.2s ease;
  min-width: 200px;
  justify-content: center;
}

.download-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.download-button img {
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
}

.prompt-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: var(--info-light);
  border-radius: 8px;
  border-left: 4px solid var(--info-color);
}

@media (max-width: 600px) {
  .download-buttons {
    flex-direction: column;
  }
}

/* Feedback Form Styles */
.feedback-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.feedback-form label {
  display: block;
  margin-bottom: 5px;
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
}

.feedback-form input[type="text"],
.feedback-form input[type="email"],
.feedback-form textarea,
.feedback-form select {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  box-sizing: border-box;
}

.feedback-form textarea {
  min-height: 150px;
  resize: vertical;
}

.feedback-form input[type="file"] {
  display: none;
}

.feedback-form button {
  background-color: var(--primary-color);
  color: var(--text-on-primary);
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  letter-spacing: 0.025em;
  transition: background-color 0.3s;
}

.feedback-form button:hover {
  background-color: var(--primary-dark);
}

.feedback-form .success-message {
  display: none;
  background-color: var(--success-light);
  color: var(--success-color);
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
  text-align: center;
}

.feedback-form .error-message {
  display: none;
  background-color: var(--danger-light);
  color: var(--danger-color);
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
  text-align: center;
}

.file-upload-label {
  display: inline-block;
  padding: 8px 16px;
  background-color: var(--light-gray);
  color: var(--text-primary);
  border-radius: 4px;
  cursor: pointer;
  margin-top: 5px;
  font-weight: var(--font-weight-medium);
}

.file-upload-label:hover {
  background-color: var(--border-dark);
}

.file-upload {
  margin-top: 5px;
}

.file-name {
  margin-left: 10px;
  font-size: var(--font-size-sm);
}

#fileInput {
  display: none;
}

/* Redirect Page Styles */
.redirect-page {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  margin: 0;
  padding: 20px;
  text-align: center;
  background-color: var(--light-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.redirect-page .loader {
  border: 5px solid var(--border-light);
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.redirect-page h1 {
  color: var(--text-primary);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
  margin-bottom: 10px;
}

.redirect-page p {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  max-width: 600px;
  margin-bottom: 20px;
}

.redirect-page a {
  color: var(--primary-color);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
}

.redirect-page a:hover {
  color: var(--primary-dark);
}

.fallback-message {
  display: none;
}

/* Fallback Header and Footer Styles */
.fallback-header {
  background: var(--surface-color);
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.fallback-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fallback-logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  text-decoration: none;
}

.fallback-nav-links a {
  margin: 0 1rem;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.fallback-footer {
  background: var(--dark-color);
  color: var(--white);
  padding: 2rem 0;
  text-align: center;
}

/* Beta Release Highlight Styles */
.highlighted-version {
  border-left: 4px solid var(--warning-color);
  padding-left: 16px;
  background-color: var(--warning-light);
  animation: highlight-fade 2s ease-in-out;
}

@keyframes highlight-fade {
  0% { background-color: var(--warning-light); opacity: 0.8; }
  100% { background-color: var(--warning-light); opacity: 0.3; }
}

/* Beta Program Styles */
.beta-container {
  max-width: 800px;
  margin: 32px auto;
  background: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 32px 20px 40px 20px;
}

.beta-container ol {
  padding-left: 24px;
  counter-reset: item;
  list-style-type: none;
}

.beta-container ol li {
  margin-bottom: 40px;
  background: var(--background-elevated);
  border-radius: 10px;
  padding: 28px 18px 26px 18px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  position: relative;
  transition: background 0.2s;
  counter-increment: item;
}

.beta-container ol li:before {
  content: counter(item);
  position: absolute;
  left: -12px;
  top: -12px;
  background: var(--primary-color);
  color: var(--white);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
}

.beta-container ol li:nth-child(odd) {
  background: var(--background-elevated);
}

.beta-container ol li:nth-child(even) {
  background: var(--primary-surface);
}

.beta-container ol li:last-child {
  margin-bottom: 0;
}

.beta-container img.instruction-img {
  display: block;
  margin: 18px auto 0 auto;
  max-width: 350px;
  width: 90vw;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  cursor: zoom-in;
  transition: box-shadow 0.2s;
}

.beta-container img.instruction-img:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.18);
}

.beta-container a {
  word-break: break-all;
}

.troubleshooting {
  margin-top: 40px;
  background: var(--info-light);
  padding: 20px 18px;
  border-radius: 10px;
  border-left: 4px solid var(--primary-color);
  font-size: var(--font-size-base);
}

.back-link {
  margin-top: 30px;
  text-align: center;
}

.back-link a {
  display: inline-block;
  padding: 10px 20px;
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  transition: background-color 0.3s;
}

.back-link a:hover {
  background-color: var(--light-gray);
}

.beta-platforms {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  margin: 40px 0;
}

.beta-platform-card {
  flex: 1;
  min-width: 280px;
  max-width: 400px;
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 24px;
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.beta-platform-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.beta-platform-card img {
  height: 60px;
  margin-bottom: 16px;
}

.beta-platform-card h3 {
  margin-bottom: 12px;
  color: var(--primary-color);
}

.beta-platform-card .status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  margin-bottom: 16px;
}

.status.available {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status.coming-soon {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.beta-platform-card .btn {
  margin-top: 16px;
}

/* Error Handling and Component Loading Styles */
.component-error {
  padding: var(--spacing-md);
  background-color: var(--warning-light);
  border: 1px solid var(--warning-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  text-align: center;
  margin: var(--spacing-md) 0;
}

.component-error a {
  color: var(--warning-color);
  text-decoration: underline;
  font-weight: var(--font-weight-medium);
}

.component-error a:hover {
  color: var(--warning-color);
  opacity: 0.8;
}

.loading-placeholder {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-muted);
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  margin: var(--spacing-md) 0;
}

.loading-placeholder::after {
  content: "Loading...";
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: "Loading"; }
  40% { content: "Loading."; }
  60% { content: "Loading.."; }
  80%, 100% { content: "Loading..."; }
}

/* Responsive Typography */
@media (max-width: 768px) {
  h1 {
    font-size: var(--font-size-3xl);
  }

  h2 {
    font-size: var(--font-size-2xl);
  }

  h3 {
    font-size: var(--font-size-xl);
  }

  .hero h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
  }

  .hero h2 {
    font-size: var(--font-size-xl);
  }

  .hero p {
    font-size: var(--font-size-lg);
  }
  
  .cta-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  nav ul li {
    margin: 0 0.5rem;
  }
  
  .download-options {
    flex-direction: column;
    align-items: center;
  }

  /* Beta responsive styles */
  .beta-container {
    padding: 16px;
    margin: 16px;
  }

  .beta-container ol li {
    padding: 16px 12px;
    font-size: var(--font-size-base);
  }

  .troubleshooting {
    padding: 12px;
    font-size: var(--font-size-sm);
  }

  .beta-platform-card {
    min-width: 100%;
  }
}

@media (max-width: 576px) {
  header .container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .hero h1 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
  }

  .hero h2 {
    font-size: var(--font-size-lg);
  }

  .hero p {
    font-size: var(--font-size-base);
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
}