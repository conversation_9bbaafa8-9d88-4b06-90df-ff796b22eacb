<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Report a Bug</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">

</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>



    <section class="hero">
        <div class="container">
            <h1>Report a Bug</h1>
            <p>Found a bug in Promz? Help us improve by reporting it here.</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="feedback-form">
                <form id="bugReportForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="bugTitle">Bug Title</label>
                        <input type="text" id="bugTitle" name="bugTitle" required placeholder="A brief description of the issue">
                    </div>

                    <div class="form-group">
                        <label for="severity">Severity</label>
                        <select id="severity" name="severity">
                            <option value="Low">Low - Minor inconvenience</option>
                            <option value="Medium" selected>Medium - Affects functionality but has workaround</option>
                            <option value="High">High - Major functionality broken</option>
                            <option value="Critical">Critical - Application crashes or data loss</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="appVersion">App Version</label>
                        <input type="text" id="appVersion" name="appVersion" placeholder="e.g., 1.2.3">
                    </div>

                    <div class="form-group">
                        <label for="platform">Platform</label>
                        <select id="platform" name="platform">
                            <option value="Windows">Windows</option>
                            <option value="macOS">macOS</option>
                            <option value="Linux">Linux</option>
                            <option value="iOS">iOS</option>
                            <option value="Android">Android</option>
                            <option value="Web">Web</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Bug Description</label>
                        <textarea id="description" name="description" required placeholder="Please describe the bug in detail. What happened? What did you expect to happen? How can we reproduce it?"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="stepsToReproduce">Steps to Reproduce</label>
                        <textarea id="stepsToReproduce" name="stepsToReproduce" placeholder="1. Open the app&#10;2. Navigate to...&#10;3. Click on..."></textarea>
                    </div>

                    <div class="form-group">
                        <label>Screenshot or Video (optional)</label>
                        <div class="file-upload">
                            <label for="fileInput" class="file-upload-label">
                                <i class="fas fa-upload"></i> Choose File
                            </label>
                            <span id="fileName" class="file-name">No file chosen</span>
                            <input type="file" id="fileInput" name="fileInput" accept="image/*,video/*">
                        </div>
                        <small>Please attach any screenshots or videos that help illustrate the issue.</small>
                    </div>

                    <button type="submit">Submit Bug Report</button>
                </form>

                <div id="successMessage" class="success-message">
                    <p>Thank you for reporting this bug! Our team will investigate and work on a fix.</p>
                </div>

                <div id="errorMessage" class="error-message">
                    <p>There was an error submitting your bug report. Please try again later or contact us <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Update file name display when a file is selected
            document.getElementById('fileInput').addEventListener('change', function() {
                const fileName = this.files[0] ? this.files[0].name : 'No file chosen';
                document.getElementById('fileName').textContent = fileName;
            });

            document.getElementById('bugReportForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                try {
                    const name = document.getElementById('name').value;
                    const email = document.getElementById('email').value;
                    const bugTitle = document.getElementById('bugTitle').value;
                    const severity = document.getElementById('severity').value;
                    const appVersion = document.getElementById('appVersion').value;
                    const platform = document.getElementById('platform').value;
                    const description = document.getElementById('description').value;
                    const stepsToReproduce = document.getElementById('stepsToReproduce').value;

                    // Construct email subject and body
                    const subject = `Bug Report: ${bugTitle}`;
                    let body = `Name: ${name}\nEmail: ${email}\nSeverity: ${severity}\nApp Version: ${appVersion}\nPlatform: ${platform}\n\n`;
                    body += `Description:\n${description}\n\n`;
                    body += `Steps to Reproduce:\n${stepsToReproduce}\n\n`;
                    body += `Note: If you have screenshots, please attach them to this email.`;

                    // Use common utilities for form submission
                    const result = await window.PromzUtils.submitForm({
                        subject: subject,
                        body: body
                    });

                    if (result.success) {
                        // Show success message
                        document.getElementById('successMessage').style.display = 'block';
                        document.getElementById('errorMessage').style.display = 'none';

                        // Reset form
                        document.getElementById('bugReportForm').reset();
                        document.getElementById('fileName').textContent = 'No file chosen';
                    } else {
                        throw new Error(result.error || 'Form submission failed');
                    }
                } catch (error) {
                    window.PromzUtils.handleError(error, 'Bug report form submission failed');
                    document.getElementById('errorMessage').style.display = 'block';
                    document.getElementById('successMessage').style.display = 'none';
                }
            });
        });
    </script>

    <script src="/js/common-utils.js"></script>
    <script src="/js/main.js"></script>
</body>
</html>
