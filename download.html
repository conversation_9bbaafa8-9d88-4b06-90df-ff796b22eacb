<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download Promz - AI Prompt Management Tool</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="images/favicon.png">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">

</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <main>
        <section class="hero">
            <div class="container">
                <h1>Someone shared a prompt with you!</h1>
                <p>Download Promz to access this prompt and many more.</p>
            </div>
        </section>

        <section>
            <div class="container">
                <div class="download-container">
                    <h2>Download Promz</h2>
                    <p><PERSON><PERSON><PERSON> is an AI assistant that helps you create, manage, and share prompts for various AI tasks. Download the app to access the shared prompt and discover many more.</p>

                    <div class="download-buttons">
                        <a href="#" class="download-button" id="ios-download">
                            <img src="images/apple-logo.svg" alt="Apple logo">
                            Download for iOS
                        </a>
                        <a href="https://play.google.com/store/apps/details?id=ai.promz" class="download-button" id="android-download">
                            <img src="images/android-logo.svg" alt="Android logo">
                            Download for Android
                        </a>
                        <a href="#" class="download-button" id="windows-download">
                            <img src="images/windows-logo.svg" alt="Windows logo">
                            Download for Windows
                        </a>
                    </div>

                    <div class="prompt-info" id="prompt-info">
                        <h3>Shared Prompt</h3>
                        <p>Someone shared a prompt with you. Download the app to access it!</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script src="/js/common-utils.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the ID from the URL
            const urlParams = new URLSearchParams(window.location.search);
            let shortId = urlParams.get('id');

            // Check if we're using path format (promz.ai/p/ID)
            if (!shortId) {
                const pathParts = window.location.pathname.split('/');
                if (pathParts.length >= 3 && pathParts[1] === 'p') {
                    shortId = pathParts[2];
                }
            }

            if (shortId) {
                // Update the prompt info section
                document.getElementById('prompt-info').innerHTML = `
                    <h3>Shared Prompt</h3>
                    <p>Someone shared a prompt with ID: ${shortId}</p>
                    <p>Download the app to access this prompt and many more!</p>
                `;

                // Set download links with the ID
                const androidLink = `intent://www.promz.ai/p/${shortId}#Intent;scheme=https;package=ai.promz;end`;
                document.getElementById('ios-download').href = 'https://apps.apple.com/app/promz/idXXXXXXXXXX';
                document.getElementById('android-download').href = androidLink;
                document.getElementById('windows-download').href = 'https://www.microsoft.com/store/apps/XXXXXXXXX';

                // For Android - check if we should auto-redirect
                const isAndroid = /Android/i.test(navigator.userAgent);
                if (isAndroid) {
                    // Redirect to the Android app
                    window.location.href = androidLink;

                    // Fallback timer - if redirect fails, show download page
                    setTimeout(function() {
                        // If we're still here, open the Play Store
                        window.location.href = 'https://play.google.com/store/apps/details?id=ai.promz';
                    }, 2000);
                }
            }
        });
    </script>

    <script src="/js/main.js"></script>
</body>
</html>
