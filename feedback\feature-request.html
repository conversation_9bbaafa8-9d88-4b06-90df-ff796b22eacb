<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Request a Feature</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
    <style>
        .feedback-form {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        input[type="text"],
        input[type="email"],
        textarea,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        textarea {
            min-height: 150px;
            resize: vertical;
        }

        button {
            background-color: #6200ee;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #3700b3;
        }

        .success-message {
            display: none;
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .error-message {
            display: none;
            background-color: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically
        fetch('../includes/header.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('feedback/')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            });
    </script>

    <section class="hero">
        <div class="container">
            <h1>Request a Feature</h1>
            <p>Have an idea for a new feature? Let us know what would make Promz even better for you!</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="feedback-form">
                <form id="featureRequestForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="featureTitle">Feature Title</label>
                        <input type="text" id="featureTitle" name="featureTitle" required placeholder="A brief title for your feature request">
                    </div>

                    <div class="form-group">
                        <label for="priority">Priority</label>
                        <select id="priority" name="priority">
                            <option value="Low">Low - Would be nice to have</option>
                            <option value="Medium" selected>Medium - Would improve my experience</option>
                            <option value="High">High - Would solve a significant problem</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Feature Description</label>
                        <textarea id="description" name="description" required placeholder="Please describe the feature you'd like to see in Promz. What problem would it solve? How would it work?"></textarea>
                    </div>

                    <button type="submit">Submit Feature Request</button>
                </form>

                <div id="successMessage" class="success-message">
                    <p>Thank you for your feature request! We'll review it and consider it for future updates.</p>
                </div>

                <div id="errorMessage" class="error-message">
                    <p>There was an error submitting your feature request. Please try again later or contact us <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically
        fetch('../includes/footer.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            });
    </script>

    <script>
        document.getElementById('featureRequestForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const featureTitle = document.getElementById('featureTitle').value;
            const priority = document.getElementById('priority').value;
            const description = document.getElementById('description').value;

            // Construct mailto link
            const subject = `Feature Request: ${featureTitle}`;
            const body = `Name: ${name}\nEmail: ${email}\nPriority: ${priority}\n\n${description}`;
            const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

            // Open email client
            window.location.href = mailtoLink;

            // Show success message
            document.getElementById('successMessage').style.display = 'block';

            // Reset form
            document.getElementById('featureRequestForm').reset();
        });
    </script>

    <script src="../js/main.js"></script>
</body>
</html>
