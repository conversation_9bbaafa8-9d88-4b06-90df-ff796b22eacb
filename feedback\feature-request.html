<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Request a Feature</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>



    <section class="hero">
        <div class="container">
            <h1>Request a Feature</h1>
            <p>Have an idea for a new feature? Let us know what would make Promz even better for you!</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="feedback-form">
                <form id="featureRequestForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="featureTitle">Feature Title</label>
                        <input type="text" id="featureTitle" name="featureTitle" required placeholder="A brief title for your feature request">
                    </div>

                    <div class="form-group">
                        <label for="priority">Priority</label>
                        <select id="priority" name="priority">
                            <option value="Low">Low - Would be nice to have</option>
                            <option value="Medium" selected>Medium - Would improve my experience</option>
                            <option value="High">High - Would solve a significant problem</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Feature Description</label>
                        <textarea id="description" name="description" required placeholder="Please describe the feature you'd like to see in Promz. What problem would it solve? How would it work?"></textarea>
                    </div>

                    <button type="submit">Submit Feature Request</button>
                </form>

                <div id="successMessage" class="success-message">
                    <p>Thank you for your feature request! We'll review it and consider it for future updates.</p>
                </div>

                <div id="errorMessage" class="error-message">
                    <p>There was an error submitting your feature request. Please try again later or contact us <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script src="/js/common-utils.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('featureRequestForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                try {
                    const name = document.getElementById('name').value;
                    const email = document.getElementById('email').value;
                    const featureTitle = document.getElementById('featureTitle').value;
                    const priority = document.getElementById('priority').value;
                    const description = document.getElementById('description').value;

                    // Use common utilities for form submission
                    const result = await window.PromzUtils.submitForm({
                        subject: `Feature Request: ${featureTitle}`,
                        body: `Name: ${name}\nEmail: ${email}\nPriority: ${priority}\n\n${description}`
                    });

                    if (result.success) {
                        // Show success message
                        document.getElementById('successMessage').style.display = 'block';
                        document.getElementById('errorMessage').style.display = 'none';

                        // Reset form
                        document.getElementById('featureRequestForm').reset();
                    } else {
                        throw new Error(result.error || 'Form submission failed');
                    }
                } catch (error) {
                    window.PromzUtils.handleError(error, 'Feature request form submission failed');
                    document.getElementById('errorMessage').style.display = 'block';
                    document.getElementById('successMessage').style.display = 'none';
                }
            });
        });
    </script>

    <script src="/js/main.js"></script>
</body>
</html>
