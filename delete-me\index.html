<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Account Deletion - Promz</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
</head>

<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically
        fetch('../includes/header.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // No specific active link for delete-me page
            });
    </script>

    <section class="page-header">
        <div class="container">
            <h1>Request Account Deletion</h1>
        </div>
    </section>

    <section class="delete-account-content">
        <div class="container">
            <div class="delete-account-intro">
                <p>If you would like to delete your Promz account and all associated data, please fill out the form
                    below. Once we receive your request, our team will process it within 30 days as outlined in our
                    Privacy Policy.</p>
                <p>Please note that this action is permanent and cannot be undone. All your data, including saved
                    prompts, settings, and usage history will be permanently deleted.</p>
            </div>

            <div class="deletion-form-container">
                <h2>Account Deletion Request</h2>
                <form id="deletion-form" class="contact-form" action="mailto:<EMAIL>" method="post"
                    enctype="text/plain">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email Address Associated with Your Account</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="userId">User ID (if known)</label>
                        <input type="text" id="userId" name="userId">
                        <small>You can find your User ID in the app settings.</small>
                    </div>
                    <div class="form-group">
                        <label for="reason">Reason for Deletion (Optional)</label>
                        <select id="reason" name="reason">
                            <option value="">-- Select a reason --</option>
                            <option value="privacy">Privacy concerns</option>
                            <option value="noUse">No longer using the app</option>
                            <option value="experience">Poor experience</option>
                            <option value="features">Missing features</option>
                            <option value="other">Other reason</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="feedback">Additional Information</label>
                        <textarea id="feedback" name="feedback" rows="4"
                            placeholder="Please include any additional information that might help us process your request."></textarea>
                    </div>
                    <div class="form-group checkbox-group">
                        <input type="checkbox" id="confirmation" name="confirmation" required>
                        <label for="confirmation">I understand that this action is permanent and cannot be
                            undone.</label>
                    </div>
                    <button type="submit" class="submit-btn">Submit Deletion Request</button>
                </form>

                <div class="alternate-methods">
                    <h3>Alternative Ways to Request Account Deletion</h3>
                    <p>If you prefer, you can also request account deletion by:</p>
                    <ul>
                        <li>Emailing us directly at <a href="mailto:<EMAIL>"><EMAIL></a> with the
                            subject line "Account Deletion Request"</li>
                        <li>Using the "Delete Account" option in the app settings</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically
        fetch('../includes/footer.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            });
    </script>

    <script src="../js/main.js"></script>
</body>

</html>