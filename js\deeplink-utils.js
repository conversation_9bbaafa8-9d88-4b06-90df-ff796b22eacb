/**
 * Deep Link Utilities - Consolidated deep link handling
 * This file provides a simple interface that uses the centralized PromzUtils
 * Replaces the old deeplink-handler.js with centralized functionality
 */

(function() {
    'use strict';
    
    /**
     * Initialize deep link handling when DOM is ready
     */
    function initDeepLinkHandling() {
        // Wait for PromzUtils to be available
        if (window.PromzUtils) {
            window.PromzUtils.debugLog('Deep link handler initialized');
            window.PromzUtils.handleDeepLink();
        } else {
            // Retry after a short delay if <PERSON>mzUtils isn't ready yet
            setTimeout(initDeepLinkHandling, 100);
        }
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDeepLinkHandling);
    } else {
        // DOM is already ready
        initDeepLinkHandling();
    }
    
    // Export for manual initialization if needed
    window.DeepLinkUtils = {
        init: initDeepLinkHandling,
        handleDeepLink: function() {
            if (window.PromzUtils) {
                return window.PromzUtils.handleDeepLink();
            } else {
                console.warn('PromzUtils not available for deep link handling');
            }
        }
    };
})();
