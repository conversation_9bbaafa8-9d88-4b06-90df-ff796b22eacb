<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Release Notes</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
    <script>
        // Version check and redirect logic
        async function checkVersionAndRedirect() {
            try {
                // Get the app version from URL parameters (sent by the app)
                const urlParams = new URLSearchParams(window.location.search);
                const appVersion = urlParams.get('version');
                const buildNumber = urlParams.get('build');
                
                // If no version provided, just show the page normally
                if (!appVersion) return;
                
                // Fetch the version registry
                const response = await fetch('/releases/versions.json');
                const data = await response.json();
                
                // Check if this version exists in production releases
                const versionInfo = data.versions.find(v => 
                    v.version === appVersion && v.inProduction === true
                );
                
                // If version not found in production releases, redirect to beta
                if (!versionInfo) {
                    console.log(`Version ${appVersion} not found in production releases, redirecting to beta`);
                    window.location.href = `/beta/releases/?version=${appVersion}&build=${buildNumber || ''}`;
                }
            } catch (error) {
                console.error('Error checking version:', error);
                // On error, stay on the current page
            }
        }
        
        // Run the check when the page loads
        window.addEventListener('DOMContentLoaded', checkVersionAndRedirect);
    </script>
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <section class="content">
        <div class="container">
            <h1>Release Notes</h1>
            
            <!-- RELEASE_NOTES_PLACEHOLDER -->
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script src="../js/common-utils.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
