/**
 * Form Utilities - Centralized form handling logic
 * Handles all form-related operations across the website
 */

class FormUtils {
    constructor() {
        this.utils = window.PromzUtils;
    }

    /**
     * Generic form submission handler
     */
    async handleFormSubmission(formId, options = {}) {
        const {
            successMessageId = 'successMessage',
            errorMessageId = 'errorMessage',
            resetForm = true,
            customSuccessCallback = null,
            customErrorCallback = null,
            validation = {}
        } = options;

        try {
            // Get form data
            const formData = this.utils.getFormData(formId);
            
            // Validate form if rules provided
            if (Object.keys(validation).length > 0) {
                const validationResult = this.utils.validateForm(formId, validation);
                if (!validationResult.valid) {
                    throw new Error(validationResult.errors.join(', '));
                }
            }

            // Build email data
            let emailData;
            if (options.emailData && typeof options.emailData === 'function') {
                emailData = options.emailData(formData);
            } else if (options.emailData) {
                emailData = options.emailData;
            } else {
                emailData = this.buildEmailData(formData, options);
            }

            // Submit form
            const result = await this.utils.submitForm(emailData);

            if (result.success) {
                // Show success message
                this.utils.showSuccessMessage(successMessageId, errorMessageId);
                
                // Reset form if requested
                if (resetForm) {
                    this.utils.resetForm(formId);
                    this.resetFileInputs(formId);
                }
                
                // Custom success callback
                if (customSuccessCallback) {
                    customSuccessCallback(formData);
                }
            } else {
                throw new Error(result.error || 'Form submission failed');
            }
        } catch (error) {
            this.utils.handleError(error, `Form submission failed: ${formId}`);
            this.utils.showErrorMessage(errorMessageId, successMessageId);
            
            // Custom error callback
            if (customErrorCallback) {
                customErrorCallback(error);
            }
        }
    }

    /**
     * Build email data from form data
     */
    buildEmailData(formData, options = {}) {
        const { subject, bodyTemplate } = options;
        
        let body = '';
        if (bodyTemplate) {
            body = bodyTemplate(formData);
        } else {
            // Default body format
            body = Object.entries(formData)
                .map(([key, value]) => `${this.formatFieldName(key)}: ${value}`)
                .join('\n');
        }

        return {
            subject: subject || 'Form Submission',
            body: body
        };
    }

    /**
     * Format field name for display
     */
    formatFieldName(fieldName) {
        return fieldName
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
    }

    /**
     * Reset file inputs in form
     */
    resetFileInputs(formId) {
        const form = this.utils.getElement(formId);
        if (form) {
            const fileInputs = form.querySelectorAll('input[type="file"]');
            fileInputs.forEach(input => {
                const displayId = input.getAttribute('data-display');
                if (displayId) {
                    this.utils.updateText(displayId, 'No file chosen');
                }
            });
        }
    }



    /**
     * Generic form setup with configuration
     */
    setupGenericForm(formId, config) {
        const form = this.utils.getElement(formId);
        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleFormSubmission(formId, config);
            });
        }
    }

    /**
     * Common validation rules
     */
    getCommonValidationRules() {
        return {
            name: { required: true },
            email: { required: true, email: true },
            subject: { required: true },
            message: { required: true, minLength: 10 },
            description: { required: true, minLength: 10 },
            title: { required: true },
            bugTitle: { required: true },
            featureTitle: { required: true }
        };
    }

    /**
     * Auto-detect and setup forms
     */
    autoSetupForms() {
        const commonRules = this.getCommonValidationRules();

        // Setup feedback form
        if (this.utils.getElement('feedbackForm')) {
            this.setupGenericForm('feedbackForm', {
                validation: {
                    name: commonRules.name,
                    email: commonRules.email,
                    subject: commonRules.subject,
                    message: commonRules.message
                },
                emailData: (data) => ({
                    subject: data.subject,
                    body: `Name: ${data.name}\nEmail: ${data.email}\n\n${data.message}`
                })
            });
        }

        // Setup feature request form
        if (this.utils.getElement('featureRequestForm')) {
            this.setupGenericForm('featureRequestForm', {
                validation: {
                    name: commonRules.name,
                    email: commonRules.email,
                    featureTitle: commonRules.featureTitle,
                    description: commonRules.description
                },
                emailData: (data) => ({
                    subject: `Feature Request: ${data.featureTitle}`,
                    body: `Name: ${data.name}\nEmail: ${data.email}\nPriority: ${data.priority}\n\n${data.description}`
                })
            });
        }

        // Setup bug report form
        if (this.utils.getElement('bugReportForm')) {
            // Setup file input first
            this.utils.setupFileInput('fileInput', 'fileName');

            this.setupGenericForm('bugReportForm', {
                validation: {
                    name: commonRules.name,
                    email: commonRules.email,
                    bugTitle: commonRules.bugTitle,
                    description: commonRules.description
                },
                emailData: (data) => ({
                    subject: `Bug Report: ${data.bugTitle}`,
                    body: `Name: ${data.name}\nEmail: ${data.email}\nSeverity: ${data.severity}\nApp Version: ${data.appVersion}\nPlatform: ${data.platform}\n\nDescription:\n${data.description}\n\nSteps to Reproduce:\n${data.stepsToReproduce}\n\nNote: If you have screenshots, please attach them to this email.`
                }),
                customSuccessCallback: () => {
                    this.utils.updateText('fileName', 'No file chosen');
                }
            });
        }
    }
}

// Initialize form utils when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.PromzUtils) {
        window.FormUtils = new FormUtils();
        window.FormUtils.autoSetupForms();
    }
});
