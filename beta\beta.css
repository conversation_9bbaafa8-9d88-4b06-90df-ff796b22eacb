/* beta.css - Improved for readability and mobile-friendliness */

/* This CSS extends the main styles.css with beta-specific styling */

.beta-container {
    max-width: 800px;
    margin: 32px auto;
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 32px 20px 40px 20px;
}

/* Beta-specific list styling */
.beta-container ol {
    padding-left: 24px;
    counter-reset: item;
    list-style-type: none;
}

.beta-container ol li {
    margin-bottom: 40px;
    background: var(--background-elevated);
    border-radius: 10px;
    padding: 28px 18px 26px 18px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    position: relative;
    transition: background 0.2s;
    counter-increment: item;
}

.beta-container ol li:before {
    content: counter(item);
    position: absolute;
    left: -12px;
    top: -12px;
    background: var(--primary-color);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.beta-container ol li:nth-child(odd) {
    background: #f9fafb;
}

.beta-container ol li:nth-child(even) {
    background: #e8f0fe;
}

.beta-container ol li:last-child {
    margin-bottom: 0;
}

img.instruction-img {
    display: block;
    margin: 18px auto 0 auto;
    max-width: 350px;
    width: 90vw;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    cursor: zoom-in;
    transition: box-shadow 0.2s;
}

img.instruction-img:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.18);
}

.beta-container a {
    word-break: break-all;
}

.troubleshooting {
    margin-top: 40px;
    background: var(--info-light);
    padding: 20px 18px;
    border-radius: 10px;
    border-left: 4px solid var(--primary-color);
    font-size: 1rem;
}

.back-link {
    margin-top: 30px;
    text-align: center;
}

.back-link a {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    transition: background-color 0.3s;
}

.back-link a:hover {
    background-color: var(--light-gray);
}

.beta-platforms {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin: 40px 0;
}

.beta-platform-card {
    flex: 1;
    min-width: 280px;
    max-width: 400px;
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 24px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.beta-platform-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.beta-platform-card img {
    height: 60px;
    margin-bottom: 16px;
}

.beta-platform-card h3 {
    margin-bottom: 12px;
    color: var(--primary-color);
}

.beta-platform-card .status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 16px;
}

.status.available {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status.coming-soon {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.beta-platform-card .btn {
    margin-top: 16px;
}

@media (max-width: 600px) {
    .beta-container {
        padding: 16px;
        margin: 16px;
    }

    .beta-container ol li {
        padding: 16px 12px;
        font-size: 1rem;
    }

    .troubleshooting {
        padding: 12px;
        font-size: 0.95rem;
    }

    .beta-platform-card {
        min-width: 100%;
    }
}