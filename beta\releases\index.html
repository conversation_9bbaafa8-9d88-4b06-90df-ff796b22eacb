<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Beta Release Notes</title>
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="icon" href="/images/favicon.png">
    <link rel="apple-touch-icon" href="/images/apple-touch-icon.png">

    <script>
        // Highlight the version that was requested
        function highlightRequestedVersion() {
            const urlParams = new URLSearchParams(window.location.search);
            const appVersion = urlParams.get('version');
            
            if (!appVersion) return;
            
            // Find the section for this version
            const versionSections = document.querySelectorAll('.release-section h2');
            let found = false;
            
            for (const section of versionSections) {
                if (section.textContent.includes(appVersion)) {
                    // Add a highlight class
                    section.parentElement.classList.add('highlighted-version');
                    // Scroll to this section
                    setTimeout(() => {
                        section.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 500); // Small delay to ensure DOM is ready
                    found = true;
                    break;
                }
            }
            
            // If we didn't find an exact match, try a partial match
            if (!found) {
                // Extract major.minor.patch without any suffix
                const versionBase = appVersion.split('-')[0];
                
                for (const section of versionSections) {
                    if (section.textContent.includes(versionBase)) {
                        section.parentElement.classList.add('highlighted-version');
                        setTimeout(() => {
                            section.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }, 500);
                        break;
                    }
                }
            }
        }
        
        // Run the check when the page loads
        window.addEventListener('DOMContentLoaded', highlightRequestedVersion);
    </script>
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <section class="content">
        <div class="container">
            <h1>Beta Release Notes</h1>
            
            <!-- LAST_UPDATED: 2025-06-11 09:13:01 -->
            <!-- LAST_VERSION:  1.0.25-beta -->
            <p><em>Last updated: 2025-06-11 09:13:01</em></p>

            <div class="release-section">
                <h2>Version  1.0.25-beta</h2>
                <h3>New Features</h3>
                <ul>
                    <li>Support for file uploads.</li>
                    <li>Added Magic Wand for improving input prompts.</li>
                    <li>Introduced prompt execution dialog to show execution progress for long running executions.</li>
                </ul>

                <h3>Bug Fixes</h3>
                <ul>
                    <li>Fixed many issues related to authentication and license.</li>
                </ul>
            </div>

            <div class="release-section">
                <h2>Version 1.0.23 (Build 24)</h2>
                <h3>New Features</h3>
                <ul>
                    <li>Recent prompts - Users can access a list of their recent prompts just like the list of popular prompts</li>
                </ul>
                
                <h3>Improvements</h3>
                <ul>
                    <li>Prevent auto-logouts after extended inactivity</li>
                    <li>Provide visual cues such as loading/ progress indicators when processing requests and loading data and responses</li>
                    <li>Show key information related to running and completed tasks on the task list so that users can distinguish, for instance, between two different executions of the same prompt with varying input parameters</li>
                    <li>Reset or update session data when the token expires and the user signs out</li>
                </ul>
            </div>

            <div class="release-section">
                <h2>Version 1.0.12 (Build 13)</h2>
                <h3>New Features</h3>
                <ul>
                    <li>Smart prompt suggestions and optimization</li>
                    <li>Popular prompts - Users can access their frequently used prompts straight from their home page</li>
                    <li>Support for custom prompts</li>
                    <li>AutoMagic prompt improvement</li>
                </ul>
                
                <h3>Improvements</h3>
                <ul>
                    <li>Performance improvements to improve startup time and execution time</li>
                    <li>The issue with the prompt outputs being cut off midway has been fixed. If you happen to run into this issue, please let us know by using our <a href="/feedback/index.html">feedback form</a></li>
                    <li>Improved layout and navigation, regardless of the number of prompts in users' prompt collection</li>
                    <li>Help/About section - A detailed Help/About section has been added to the app with app usage guidance</li>
                    <li>Issue with auto-logouts after extended inactivity has been fixed. If you happen to run into this issue, please let us know by using our <a href="/feedback/index.html">feedback form</a></li>
                    <li>Privacy section has been added addressing data storage, retention and privacy concerns</li>
                    <li>Website reorganization and enhanced feedback functionality</li>
                </ul>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script src="/js/common-utils.js"></script>
</body>
</html>
