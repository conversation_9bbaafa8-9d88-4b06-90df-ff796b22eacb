<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help & Documentation - Promz</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="images/favicon.png">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">
    <style>
        .help-section {
            margin-bottom: var(--spacing-xl);
        }

        .help-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .help-card {
            background-color: var(--surface-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .help-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .help-card h3 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
        }

        .help-card .icon {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-sm);
        }

        .resource-links {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .resource-link {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background-color: var(--primary-surface);
            color: var(--primary-color);
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            transition: background-color 0.3s ease;
        }

        .resource-link:hover {
            background-color: var(--primary-color);
            color: var(--text-on-primary);
        }

        .faq-item {
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md);
            background-color: var(--light-color);
            border-radius: var(--border-radius);
        }

        .faq-question {
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .contact-info {
            background-color: var(--info-light);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--info-color);
            margin-top: var(--spacing-lg);
        }
    </style>
</head>

<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically
        fetch('/includes/header.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('help.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            });
    </script>

    <section class="page-header">
        <div class="container">
            <h1>Help & Documentation</h1>
            <p>Find answers, guides, and resources to get the most out of Promz</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="help-section">
                <h2>Getting Started</h2>
                <div class="help-grid">
                    <div class="help-card">
                        <div class="icon">🚀</div>
                        <h3>Quick Start Guide</h3>
                        <p>Learn the basics of Promz and get up and running in minutes.</p>
                        <div class="resource-links">
                            <a href="/beta/index.html" class="resource-link">Join Beta Program</a>
                            <a href="/download.html" class="resource-link">Download App</a>
                        </div>
                    </div>

                    <div class="help-card">
                        <div class="icon">📋</div>
                        <h3>Managing Prompts</h3>
                        <p>Organize, categorize, and optimize your AI prompts for maximum effectiveness.</p>
                        <div class="resource-links">
                            <a href="/about.html" class="resource-link">Learn More</a>
                        </div>
                    </div>

                    <div class="help-card">
                        <div class="icon">👥</div>
                        <h3>Collaboration</h3>
                        <p>Share prompts with your team and collaborate in real-time.</p>
                        <div class="resource-links">
                            <a href="/about.html" class="resource-link">Features Overview</a>
                        </div>
                    </div>

                    <div class="help-card">
                        <div class="icon">🔧</div>
                        <h3>Troubleshooting</h3>
                        <p>Common issues and solutions to help you resolve problems quickly.</p>
                        <div class="resource-links">
                            <a href="/feedback/bug-report.html" class="resource-link">Report Bug</a>
                            <a href="/feedback/index.html" class="resource-link">Contact Support</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="help-section">
                <h2>Frequently Asked Questions</h2>
                
                <div class="faq-item">
                    <div class="faq-question">How do I get started with Promz?</div>
                    <p>Download the app from your platform's app store, create an account, and start organizing your AI prompts. Check out our beta program for early access to new features.</p>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Is Promz available on all platforms?</div>
                    <p>Yes! Promz is available on iOS, Android, and Windows. We're built with Flutter for true cross-platform compatibility.</p>
                </div>

                <div class="faq-item">
                    <div class="faq-question">How does real-time collaboration work?</div>
                    <p>Promz uses advanced synchronization technology to allow multiple team members to work on prompts simultaneously with automatic conflict resolution.</p>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Can I share prompts with others?</div>
                    <p>Absolutely! You can share individual prompts or entire collections with team members or the community.</p>
                </div>

                <div class="faq-item">
                    <div class="faq-question">What AI providers does Promz support?</div>
                    <p>Promz integrates with popular AI providers and platforms. Check our features page for the most up-to-date list of supported services.</p>
                </div>
            </div>

            <div class="help-section">
                <h2>Additional Resources</h2>
                <div class="help-grid">
                    <div class="help-card">
                        <div class="icon">📖</div>
                        <h3>Release Notes</h3>
                        <p>Stay updated with the latest features and improvements.</p>
                        <div class="resource-links">
                            <a href="/releases/index.html" class="resource-link">View Release Notes</a>
                            <a href="/beta/releases/index.html" class="resource-link">Beta Release Notes</a>
                        </div>
                    </div>

                    <div class="help-card">
                        <div class="icon">💬</div>
                        <h3>Feedback & Support</h3>
                        <p>Get help, report issues, or suggest new features.</p>
                        <div class="resource-links">
                            <a href="/feedback/index.html" class="resource-link">Send Feedback</a>
                            <a href="/feedback/feature-request.html" class="resource-link">Request Feature</a>
                            <a href="/feedback/bug-report.html" class="resource-link">Report Bug</a>
                        </div>
                    </div>

                    <div class="help-card">
                        <div class="icon">⚖️</div>
                        <h3>Legal & Privacy</h3>
                        <p>Review our terms of service and privacy policy.</p>
                        <div class="resource-links">
                            <a href="/terms.html" class="resource-link">Terms of Service</a>
                            <a href="/privacy.html" class="resource-link">Privacy Policy</a>
                        </div>
                    </div>
                </div>

                <div class="contact-info">
                    <h3>Need More Help?</h3>
                    <p>If you can't find what you're looking for, don't hesitate to reach out to our support team.</p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p><strong>General Inquiries:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically
        fetch('/includes/footer.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            });
    </script>

    <script src="js/main.js"></script>
</body>
</html>
