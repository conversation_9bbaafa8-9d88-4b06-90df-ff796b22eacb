<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Send Feedback</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">

</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>



    <section class="hero">
        <div class="container">
            <h1>Send Feedback</h1>
            <p>We value your feedback and are constantly working to improve Promz. Please let us know your thoughts!</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="feedback-form">
                <form id="feedbackForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>

                    <div class="form-group">
                        <label for="message">Your Feedback</label>
                        <textarea id="message" name="message" required></textarea>
                    </div>

                    <button type="submit">Send Feedback</button>
                </form>

                <div id="successMessage" class="success-message">
                    <p>Thank you for your feedback! We'll review it and get back to you if needed.</p>
                </div>

                <div id="errorMessage" class="error-message">
                    <p>There was an error sending your feedback. Please try again later or contact us <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>



    <script src="/js/common-utils.js"></script>
    <script src="/js/form-utils.js"></script>
    <script src="/js/page-utils.js"></script>
    <script src="/js/main.js"></script>

    <script>
        // Debug script to check form initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Feedback page loaded');

            // Check if messages are hidden by default
            const successMsg = document.getElementById('successMessage');
            const errorMsg = document.getElementById('errorMessage');

            console.log('Success message display:', successMsg ? getComputedStyle(successMsg).display : 'not found');
            console.log('Error message display:', errorMsg ? getComputedStyle(errorMsg).display : 'not found');

            // Check if form utils are loaded
            console.log('PromzUtils available:', !!window.PromzUtils);
            console.log('FormUtils available:', !!window.FormUtils);

            // Ensure messages are hidden
            if (window.PromzUtils) {
                window.PromzUtils.hideElement('successMessage');
                window.PromzUtils.hideElement('errorMessage');
            }
        });
    </script>
</body>
</html>
