<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Send Feedback</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">
    <style>
        .feedback-form {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: var(--surface-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.5;
            color: var(--text-primary);
        }

        input[type="text"],
        input[type="email"],
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
        }

        textarea {
            min-height: 150px;
            resize: vertical;
        }

        button {
            background-color: var(--primary-color);
            color: var(--text-on-primary);
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.5;
            letter-spacing: 0.025em;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: var(--primary-dark);
        }

        .success-message {
            display: none;
            background-color: var(--success-light);
            color: var(--success-color);
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .error-message {
            display: none;
            background-color: var(--danger-light);
            color: var(--danger-color);
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>

    <script>
        // Load header dynamically
        fetch('/includes/header.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('header-placeholder').innerHTML = data;
                // Update active link in header
                document.querySelectorAll('nav a').forEach(link => {
                    if (link.href.includes('feedback/index.html')) {
                        link.classList.add('active');
                    } else {
                        link.classList.remove('active');
                    }
                });
            });
    </script>

    <section class="hero">
        <div class="container">
            <h1>Send Feedback</h1>
            <p>We value your feedback and are constantly working to improve Promz. Please let us know your thoughts!</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="feedback-form">
                <form id="feedbackForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>

                    <div class="form-group">
                        <label for="message">Your Feedback</label>
                        <textarea id="message" name="message" required></textarea>
                    </div>

                    <button type="submit">Send Feedback</button>
                </form>

                <div id="successMessage" class="success-message">
                    <p>Thank you for your feedback! We'll review it and get back to you if needed.</p>
                </div>

                <div id="errorMessage" class="error-message">
                    <p>There was an error sending your feedback. Please try again later or contact us <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>

    <script>
        // Load footer dynamically
        fetch('/includes/footer.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('footer-placeholder').innerHTML = data;
            });
    </script>

    <script>
        document.getElementById('feedbackForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;

            // Construct mailto link
            const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(`Name: ${name}\nEmail: ${email}\n\n${message}`)}`;

            // Open email client
            window.location.href = mailtoLink;

            // Show success message
            document.getElementById('successMessage').style.display = 'block';

            // Reset form
            document.getElementById('feedbackForm').reset();
        });
    </script>

    <script src="../js/main.js"></script>
</body>
</html>
