<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promz - Send Feedback</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="icon" href="../images/favicon.png">
    <link rel="apple-touch-icon" href="../images/apple-touch-icon.png">

</head>
<body>
    <!-- Include header -->
    <div id="header-placeholder"></div>



    <section class="hero">
        <div class="container">
            <h1>Send Feedback</h1>
            <p>We value your feedback and are constantly working to improve Promz. Please let us know your thoughts!</p>
        </div>
    </section>

    <section class="content">
        <div class="container">
            <div class="feedback-form">
                <form id="feedbackForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>

                    <div class="form-group">
                        <label for="message">Your Feedback</label>
                        <textarea id="message" name="message" required></textarea>
                    </div>

                    <button type="submit">Send Feedback</button>
                </form>

                <div id="successMessage" class="success-message">
                    <p>Thank you for your feedback! We'll review it and get back to you if needed.</p>
                </div>

                <div id="errorMessage" class="error-message">
                    <p>There was an error sending your feedback. Please try again later or contact us <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Include footer -->
    <div id="footer-placeholder"></div>



    <script src="/js/common-utils.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                try {
                    const name = document.getElementById('name').value;
                    const email = document.getElementById('email').value;
                    const subject = document.getElementById('subject').value;
                    const message = document.getElementById('message').value;

                    // Use common utilities for form submission
                    const result = await window.PromzUtils.submitForm({
                        subject: subject,
                        body: `Name: ${name}\nEmail: ${email}\n\n${message}`
                    });

                    if (result.success) {
                        // Show success message
                        document.getElementById('successMessage').style.display = 'block';
                        document.getElementById('errorMessage').style.display = 'none';

                        // Reset form
                        document.getElementById('feedbackForm').reset();
                    } else {
                        throw new Error(result.error || 'Form submission failed');
                    }
                } catch (error) {
                    window.PromzUtils.handleError(error, 'Feedback form submission failed');
                    document.getElementById('errorMessage').style.display = 'block';
                    document.getElementById('successMessage').style.display = 'none';
                }
            });
        });
    </script>

    <script src="/js/main.js"></script>
</body>
</html>
