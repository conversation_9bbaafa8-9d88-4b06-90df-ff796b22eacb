/**
 * Promz Static Website JavaScript
 * Provides interactivity for the Promz marketing website
 * Uses PromzUtils for common functionality and error handling
 */

document.addEventListener('DOMContentLoaded', function () {
    // Note: Mobile navigation and smooth scrolling are now handled by page-utils.js

    // Add animation class to elements when they're in viewport
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.feature-card, .download-button');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementTop < windowHeight - 100) {
                element.classList.add('animate');
            }
        });
    };

    // Initialize animations
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Run once on page load

    // Current year for copyright
    const yearElement = document.querySelector('.copyright-year');
    if (yearElement) {
        yearElement.textContent = new Date().getFullYear();
    }
});