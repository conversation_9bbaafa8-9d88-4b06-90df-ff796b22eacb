/**
 * Page Utilities - Centralized page-specific logic
 * Handles common page operations and interactions
 */

class PageUtils {
    constructor() {
        this.utils = window.PromzUtils;
    }

    /**
     * Download page logic
     */
    setupDownloadPage() {
        const shortId = this.utils.getShortId();
        
        if (shortId) {
            // Update prompt info section
            this.utils.updateContent('prompt-info', `
                <h3>Shared Prompt</h3>
                <p>Someone shared a prompt with ID: ${shortId}</p>
                <p>Download the app to access this prompt and many more!</p>
            `);

            // Set download links with the ID
            this.updateDownloadLinks(shortId);

            // Auto-redirect for Android
            if (this.utils.isAndroid()) {
                this.utils.redirectToApp(shortId);
            }
        }
    }

    /**
     * Update download links with short ID
     */
    updateDownloadLinks(shortId) {
        const links = {
            'ios-download': this.utils.generateAppLink(shortId, 'ios'),
            'android-download': this.utils.generateAppLink(shortId, 'android'),
            'windows-download': this.utils.generateAppLink(shortId, 'windows')
        };

        Object.entries(links).forEach(([id, href]) => {
            const element = this.utils.getElement(id);
            if (element) {
                element.href = href;
            }
        });
    }

    /**
     * Redirect page logic
     */
    setupRedirectPage() {
        // Show fallback message after delay
        this.utils.safeTimeout(() => {
            this.utils.removeClass('fallback-message', 'fallback-message');
            
            // Update fallback link with ID if available
            const shortId = this.utils.getIdFromPath();
            if (shortId) {
                const fallbackLink = this.utils.getElement('fallback-link');
                if (fallbackLink) {
                    fallbackLink.href = `/download.html?id=${shortId}`;
                }
            }
        }, 3000);
    }

    /**
     * Releases page logic
     */
    async setupReleasesPage() {
        try {
            const urlParams = this.utils.getUrlParams();
            const appVersion = urlParams.get('version');
            const buildNumber = urlParams.get('build');
            
            // If no version provided, show page normally
            if (!appVersion) return;
            
            // Use centralized fetch with caching
            const data = await this.utils.fetchWithCache('/releases/versions.json');
            const parsedData = JSON.parse(data);
            
            // Check if version exists in production releases
            const versionInfo = parsedData.versions.find(v => 
                v.version === appVersion && v.inProduction === true
            );
            
            // Redirect to beta if not found in production
            if (!versionInfo) {
                this.utils.debugLog(`Version ${appVersion} not found in production, redirecting to beta`);
                window.location.href = `/beta/releases/?version=${appVersion}&build=${buildNumber || ''}`;
            }
        } catch (error) {
            this.utils.handleError(error, 'Version check failed');
        }
    }

    /**
     * Beta releases page logic
     */
    async setupBetaReleasesPage() {
        try {
            const urlParams = this.utils.getUrlParams();
            const highlightVersion = urlParams.get('version');
            
            if (highlightVersion) {
                // Highlight the specific version
                this.highlightVersion(highlightVersion);
            }
        } catch (error) {
            this.utils.handleError(error, 'Beta releases setup failed');
        }
    }

    /**
     * Highlight specific version in releases
     */
    highlightVersion(version) {
        // Find and highlight version elements
        const versionElements = document.querySelectorAll('.release-section');
        versionElements.forEach(element => {
            if (element.textContent.includes(version)) {
                element.classList.add('highlighted-version');
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }

    /**
     * Setup mobile navigation (enhanced version)
     */
    setupMobileNavigation() {
        try {
            // Create mobile nav toggle if it doesn't exist
            let navToggle = document.querySelector('.mobile-nav-toggle');
            const header = document.querySelector('header .container');
            const nav = document.querySelector('nav');

            if (!header || !nav) {
                this.utils.debugLog('Header or nav not found for mobile navigation setup');
                return;
            }

            // Create toggle button if it doesn't exist
            if (!navToggle && window.innerWidth <= 768) {
                navToggle = document.createElement('button');
                navToggle.classList.add('mobile-nav-toggle');
                navToggle.setAttribute('aria-label', 'Toggle navigation menu');
                navToggle.innerHTML = '<span></span><span></span><span></span>';
                header.insertBefore(navToggle, nav);
                nav.classList.add('mobile-nav');
            }

            // Add click handler
            if (navToggle) {
                navToggle.addEventListener('click', () => {
                    try {
                        nav.classList.toggle('open');
                        navToggle.classList.toggle('open');
                    } catch (error) {
                        this.utils.handleError(error, 'Mobile menu toggle failed');
                    }
                });
            }

            // Handle window resize
            const handleResize = () => {
                if (window.innerWidth > 768) {
                    nav.classList.remove('open', 'mobile-nav');
                    if (navToggle) {
                        navToggle.classList.remove('open');
                        navToggle.remove();
                        navToggle = null;
                    }
                } else if (!navToggle) {
                    this.setupMobileNavigation();
                }
            };

            window.addEventListener('resize', handleResize);
        } catch (error) {
            this.utils.handleError(error, 'Mobile navigation setup failed');
        }
    }

    /**
     * Setup smooth scrolling for anchor links (enhanced version)
     */
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                try {
                    e.preventDefault();

                    const targetId = anchor.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        // Smooth scroll with offset for fixed header
                        window.scrollTo({
                            top: targetElement.offsetTop - 80,
                            behavior: 'smooth'
                        });

                        // Close mobile nav if open
                        const mobileNav = document.querySelector('nav.mobile-nav');
                        const navToggle = document.querySelector('.mobile-nav-toggle');
                        if (mobileNav && mobileNav.classList.contains('open')) {
                            mobileNav.classList.remove('open');
                            if (navToggle) {
                                navToggle.classList.remove('open');
                            }
                        }
                    }
                } catch (error) {
                    this.utils.handleError(error, 'Smooth scrolling failed');
                }
            });
        });
    }

    /**
     * Auto-detect page and setup appropriate functionality
     */
    autoSetupPage() {
        const path = window.location.pathname;
        
        // Setup based on current page
        if (path.includes('download.html') || path.includes('/download/')) {
            this.setupDownloadPage();
        }
        
        if (path.includes('redirect.html') || path.includes('/redirect/')) {
            this.setupRedirectPage();
        }
        
        if (path.includes('releases/index.html') || path.endsWith('/releases/')) {
            this.setupReleasesPage();
        }
        
        if (path.includes('beta/releases/')) {
            this.setupBetaReleasesPage();
        }
        
        // Setup common functionality for all pages
        this.setupMobileNavigation();
        this.setupSmoothScrolling();
    }
}

// Initialize page utils when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.PromzUtils) {
        window.PageUtils = new PageUtils();
        window.PageUtils.autoSetupPage();
    }
});
