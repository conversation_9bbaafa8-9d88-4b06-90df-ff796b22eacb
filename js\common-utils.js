/**
 * Common Utilities for Promz Website
 * Provides centralized error handling, caching, and common functionality
 */

class PromzUtils {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = new Map();
        this.defaultCacheTime = 5 * 60 * 1000; // 5 minutes
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
        
        // Initialize error tracking
        this.errorLog = [];
        this.maxErrorLog = 50;
        
        // Bind methods to preserve context
        this.fetchWithCache = this.fetchWithCache.bind(this);
        this.handleError = this.handleError.bind(this);
        this.loadComponent = this.loadComponent.bind(this);
    }

    /**
     * Enhanced fetch with caching, retry logic, and error handling
     */
    async fetchWithCache(url, options = {}) {
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        const now = Date.now();
        
        // Check cache first
        if (this.cache.has(cacheKey) && this.cacheExpiry.get(cacheKey) > now) {
            this.debugLog(`Cache hit for: ${url}`);
            return this.cache.get(cacheKey);
        }

        // Attempt fetch with retry logic
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                this.debugLog(`Fetching ${url} (attempt ${attempt})`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
                
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.text();
                
                // Cache successful response
                this.cache.set(cacheKey, data);
                this.cacheExpiry.set(cacheKey, now + this.defaultCacheTime);
                
                this.debugLog(`Successfully fetched and cached: ${url}`);
                return data;
                
            } catch (error) {
                this.handleError(error, `Fetch attempt ${attempt} failed for ${url}`);
                
                if (attempt === this.retryAttempts) {
                    // Final attempt failed, return fallback
                    return this.getFallbackContent(url);
                }
                
                // Wait before retry
                await this.delay(this.retryDelay * attempt);
            }
        }
    }

    /**
     * Load header or footer component with error handling
     */
    async loadComponent(elementId, componentPath, activePageCheck = null) {
        try {
            const data = await this.fetchWithCache(componentPath);
            const element = document.getElementById(elementId);
            
            if (!element) {
                throw new Error(`Element with ID '${elementId}' not found`);
            }
            
            element.innerHTML = data;
            
            // Handle active page highlighting if provided
            if (activePageCheck) {
                this.setActiveNavigation(activePageCheck);
            }
            
            this.debugLog(`Successfully loaded component: ${componentPath}`);
            
        } catch (error) {
            this.handleError(error, `Failed to load component: ${componentPath}`);
            this.showComponentError(elementId, componentPath);
        }
    }

    /**
     * Set active navigation link
     */
    setActiveNavigation(pageCheck) {
        try {
            document.querySelectorAll('nav a').forEach(link => {
                if (pageCheck(link)) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
        } catch (error) {
            this.handleError(error, 'Failed to set active navigation');
        }
    }

    /**
     * Centralized error handling
     */
    handleError(error, context = '') {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            error: error.message || error.toString(),
            context: context,
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        // Add to error log
        this.errorLog.push(errorInfo);
        if (this.errorLog.length > this.maxErrorLog) {
            this.errorLog.shift(); // Remove oldest error
        }
        
        // Log to console in development
        if (this.isDevelopment()) {
            console.error(`[PromzUtils Error] ${context}:`, error);
        }
        
        // Store in localStorage for debugging
        try {
            localStorage.setItem('promz_errors', JSON.stringify(this.errorLog.slice(-10)));
        } catch (e) {
            // localStorage might be full or unavailable
        }
    }

    /**
     * Show user-friendly error message for failed components
     */
    showComponentError(elementId, componentPath) {
        const element = document.getElementById(elementId);
        if (element) {
            const componentName = componentPath.includes('header') ? 'navigation' : 'footer';
            element.innerHTML = `
                <div class="component-error">
                    <p>Unable to load ${componentName}. <a href="javascript:location.reload()">Refresh page</a> to try again.</p>
                </div>
            `;
        }
    }

    /**
     * Get fallback content for failed requests
     */
    getFallbackContent(url) {
        if (url.includes('header.html')) {
            return `
                <header class="fallback-header">
                    <div class="container">
                        <nav class="fallback-nav">
                            <a href="/index.html" class="fallback-logo">Promz</a>
                            <div class="fallback-nav-links">
                                <a href="/index.html">Home</a>
                                <a href="/about.html">About</a>
                                <a href="/download.html">Download</a>
                            </div>
                        </nav>
                    </div>
                </header>
            `;
        } else if (url.includes('footer.html')) {
            return `
                <footer class="fallback-footer">
                    <div class="container">
                        <p>&copy; 2025 Promz. All rights reserved.</p>
                    </div>
                </footer>
            `;
        }
        return '';
    }

    /**
     * Utility functions
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    isDevelopment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.hostname.includes('dev');
    }

    debugLog(message) {
        if (this.isDevelopment()) {
            console.log(`[PromzUtils] ${message}`);
        }
    }

    /**
     * Clear cache manually
     */
    clearCache() {
        this.cache.clear();
        this.cacheExpiry.clear();
        this.debugLog('Cache cleared');
    }

    /**
     * Get error log for debugging
     */
    getErrorLog() {
        return this.errorLog;
    }

    /**
     * Form submission with error handling
     */
    async submitForm(formData, action = 'mailto') {
        try {
            if (action === 'mailto') {
                const mailtoLink = this.createMailtoLink(formData);
                window.location.href = mailtoLink;
                return { success: true };
            }
            // Add other submission methods here if needed
        } catch (error) {
            this.handleError(error, 'Form submission failed');
            return { success: false, error: error.message };
        }
    }

    /**
     * Create mailto link from form data
     */
    createMailtoLink(formData) {
        const { to = '<EMAIL>', subject, body } = formData;
        return `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    }

    /**
     * Initialize common functionality
     */
    init() {
        // Load header and footer for all pages
        this.loadComponent('header-placeholder', '/includes/header.html', (link) => {
            return link.href.includes(window.location.pathname.split('/').pop() || 'index.html');
        });
        
        this.loadComponent('footer-placeholder', '/includes/footer.html');
        
        // Set up global error handler
        window.addEventListener('error', (event) => {
            this.handleError(event.error, 'Global error handler');
        });
        
        // Set up unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'Unhandled promise rejection');
        });
        
        this.debugLog('PromzUtils initialized');
    }
}

// Create global instance
window.PromzUtils = new PromzUtils();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => window.PromzUtils.init());
} else {
    window.PromzUtils.init();
}
