/**
 * Common Utilities for Promz Website
 * Provides centralized error handling, caching, and common functionality
 */

class PromzUtils {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = new Map();
        this.defaultCacheTime = 5 * 60 * 1000; // 5 minutes
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
        
        // Initialize error tracking
        this.errorLog = [];
        this.maxErrorLog = 50;
        
        // Bind methods to preserve context
        this.fetchWithCache = this.fetchWithCache.bind(this);
        this.handleError = this.handleError.bind(this);
        this.loadComponent = this.loadComponent.bind(this);
    }

    /**
     * Enhanced fetch with caching, retry logic, and error handling
     */
    async fetchWithCache(url, options = {}) {
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        const now = Date.now();
        
        // Check cache first
        if (this.cache.has(cacheKey) && this.cacheExpiry.get(cacheKey) > now) {
            this.debugLog(`Cache hit for: ${url}`);
            return this.cache.get(cacheKey);
        }

        // Attempt fetch with retry logic
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                this.debugLog(`Fetching ${url} (attempt ${attempt})`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
                
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.text();
                
                // Cache successful response
                this.cache.set(cacheKey, data);
                this.cacheExpiry.set(cacheKey, now + this.defaultCacheTime);
                
                this.debugLog(`Successfully fetched and cached: ${url}`);
                return data;
                
            } catch (error) {
                this.handleError(error, `Fetch attempt ${attempt} failed for ${url}`);
                
                if (attempt === this.retryAttempts) {
                    // Final attempt failed, return fallback
                    return this.getFallbackContent(url);
                }
                
                // Wait before retry
                await this.delay(this.retryDelay * attempt);
            }
        }
    }

    /**
     * Load header or footer component with error handling
     */
    async loadComponent(elementId, componentPath, activePageCheck = null) {
        try {
            const data = await this.fetchWithCache(componentPath);
            const element = document.getElementById(elementId);
            
            if (!element) {
                throw new Error(`Element with ID '${elementId}' not found`);
            }
            
            element.innerHTML = data;
            
            // Handle active page highlighting if provided
            if (activePageCheck) {
                this.setActiveNavigation(activePageCheck);
            }
            
            this.debugLog(`Successfully loaded component: ${componentPath}`);
            
        } catch (error) {
            this.handleError(error, `Failed to load component: ${componentPath}`);
            this.showComponentError(elementId, componentPath);
        }
    }

    /**
     * Set active navigation link
     */
    setActiveNavigation(pageCheck) {
        try {
            document.querySelectorAll('nav a').forEach(link => {
                if (pageCheck(link)) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
        } catch (error) {
            this.handleError(error, 'Failed to set active navigation');
        }
    }

    /**
     * Centralized error handling
     */
    handleError(error, context = '') {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            error: error.message || error.toString(),
            context: context,
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        // Add to error log
        this.errorLog.push(errorInfo);
        if (this.errorLog.length > this.maxErrorLog) {
            this.errorLog.shift(); // Remove oldest error
        }
        
        // Log to console in development
        if (this.isDevelopment()) {
            console.error(`[PromzUtils Error] ${context}:`, error);
        }
        
        // Store in localStorage for debugging
        try {
            localStorage.setItem('promz_errors', JSON.stringify(this.errorLog.slice(-10)));
        } catch (e) {
            // localStorage might be full or unavailable
        }
    }

    /**
     * Show user-friendly error message for failed components
     */
    showComponentError(elementId, componentPath) {
        const element = document.getElementById(elementId);
        if (element) {
            const componentName = componentPath.includes('header') ? 'navigation' : 'footer';
            element.innerHTML = `
                <div class="component-error">
                    <p>Unable to load ${componentName}. <a href="javascript:location.reload()">Refresh page</a> to try again.</p>
                </div>
            `;
        }
    }

    /**
     * Get fallback content for failed requests
     */
    getFallbackContent(url) {
        if (url.includes('header.html')) {
            return `
                <header class="fallback-header">
                    <div class="container">
                        <nav class="fallback-nav">
                            <a href="/index.html" class="fallback-logo">Promz</a>
                            <div class="fallback-nav-links">
                                <a href="/index.html">Home</a>
                                <a href="/about.html">About</a>
                                <a href="/download.html">Download</a>
                            </div>
                        </nav>
                    </div>
                </header>
            `;
        } else if (url.includes('footer.html')) {
            return `
                <footer class="fallback-footer">
                    <div class="container">
                        <p>&copy; 2025 Promz. All rights reserved.</p>
                    </div>
                </footer>
            `;
        }
        return '';
    }

    /**
     * Utility functions
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    isDevelopment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.hostname.includes('dev');
    }

    debugLog(message) {
        if (this.isDevelopment()) {
            console.log(`[PromzUtils] ${message}`);
        }
    }

    /**
     * Clear cache manually
     */
    clearCache() {
        this.cache.clear();
        this.cacheExpiry.clear();
        this.debugLog('Cache cleared');
    }

    /**
     * Get error log for debugging
     */
    getErrorLog() {
        return this.errorLog;
    }

    /**
     * DOM Manipulation Utilities
     */

    // Safe element selection
    getElement(id) {
        const element = document.getElementById(id);
        if (!element) {
            this.debugLog(`Element with ID '${id}' not found`);
        }
        return element;
    }

    // Safe element selection with error handling
    getElementSafe(id) {
        try {
            return this.getElement(id);
        } catch (error) {
            this.handleError(error, `Failed to get element: ${id}`);
            return null;
        }
    }

    // Show/hide elements using CSS classes
    showElement(id) {
        const element = this.getElement(id);
        if (element) {
            element.classList.remove('hidden');
            element.style.display = 'block';
        }
    }

    hideElement(id) {
        const element = this.getElement(id);
        if (element) {
            element.classList.add('hidden');
            element.style.display = 'none';
        }
    }

    // Toggle element visibility
    toggleElement(id) {
        const element = this.getElement(id);
        if (element) {
            element.classList.toggle('hidden');
            const isHidden = element.classList.contains('hidden');
            element.style.display = isHidden ? 'none' : 'block';
        }
    }

    // Update element content safely
    updateContent(id, content) {
        const element = this.getElement(id);
        if (element) {
            element.innerHTML = content;
        }
    }

    // Update element text safely
    updateText(id, text) {
        const element = this.getElement(id);
        if (element) {
            element.textContent = text;
        }
    }

    // Add class to element
    addClass(id, className) {
        const element = this.getElement(id);
        if (element) {
            element.classList.add(className);
        }
    }

    // Remove class from element
    removeClass(id, className) {
        const element = this.getElement(id);
        if (element) {
            element.classList.remove(className);
        }
    }

    /**
     * Message Display Utilities
     */

    // Show success message
    showSuccessMessage(messageId = 'successMessage', errorId = 'errorMessage') {
        this.showElement(messageId);
        this.hideElement(errorId);
    }

    // Show error message
    showErrorMessage(messageId = 'errorMessage', successId = 'successMessage') {
        this.showElement(messageId);
        this.hideElement(successId);
    }

    // Clear all messages
    clearMessages(successId = 'successMessage', errorId = 'errorMessage') {
        this.hideElement(successId);
        this.hideElement(errorId);
    }

    /**
     * Form Utilities
     */

    // Get form data as object
    getFormData(formId) {
        const form = this.getElement(formId);
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    }

    // Reset form safely
    resetForm(formId) {
        const form = this.getElement(formId);
        if (form) {
            form.reset();
        }
    }

    // Validate required fields
    validateRequiredFields(formId) {
        const form = this.getElement(formId);
        if (!form) return false;

        const requiredFields = form.querySelectorAll('[required]');
        for (let field of requiredFields) {
            if (!field.value.trim()) {
                field.focus();
                return false;
            }
        }
        return true;
    }

    /**
     * Form submission with error handling
     */
    async submitForm(formData, action = 'mailto') {
        try {
            if (action === 'mailto') {
                const mailtoLink = this.createMailtoLink(formData);
                window.location.href = mailtoLink;
                return { success: true };
            }
            // Add other submission methods here if needed
        } catch (error) {
            this.handleError(error, 'Form submission failed');
            return { success: false, error: error.message };
        }
    }

    /**
     * Create mailto link from form data
     */
    createMailtoLink(formData) {
        const { to = '<EMAIL>', subject, body } = formData;
        return `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    }

    /**
     * File Handling Utilities
     */

    // Setup file input handler
    setupFileInput(inputId, displayId) {
        const fileInput = this.getElement(inputId);
        const displayElement = this.getElement(displayId);

        if (fileInput && displayElement) {
            fileInput.addEventListener('change', (e) => {
                const fileName = e.target.files[0] ? e.target.files[0].name : 'No file chosen';
                displayElement.textContent = fileName;
            });
        }
    }

    // Get file name from input
    getFileName(inputId) {
        const fileInput = this.getElement(inputId);
        return fileInput && fileInput.files[0] ? fileInput.files[0].name : null;
    }

    /**
     * Timer and Delay Utilities
     */

    // Safe setTimeout with error handling
    safeTimeout(callback, delay) {
        try {
            return setTimeout(() => {
                try {
                    callback();
                } catch (error) {
                    this.handleError(error, 'Timer callback failed');
                }
            }, delay);
        } catch (error) {
            this.handleError(error, 'Failed to set timeout');
            return null;
        }
    }

    // Delay function for async operations
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Validation Utilities
     */

    // Validate email format
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Validate required field
    isFieldValid(value) {
        return value && value.trim().length > 0;
    }

    // Validate form with custom rules
    validateForm(formId, rules = {}) {
        const form = this.getElement(formId);
        if (!form) return { valid: false, errors: ['Form not found'] };

        const errors = [];
        const formData = this.getFormData(formId);

        // Check required fields
        for (let [field, value] of Object.entries(formData)) {
            if (rules[field]?.required && !this.isFieldValid(value)) {
                errors.push(`${field} is required`);
            }

            if (rules[field]?.email && value && !this.isValidEmail(value)) {
                errors.push(`${field} must be a valid email`);
            }

            if (rules[field]?.minLength && value && value.length < rules[field].minLength) {
                errors.push(`${field} must be at least ${rules[field].minLength} characters`);
            }
        }

        return { valid: errors.length === 0, errors };
    }

    /**
     * App Integration and Deep Link Utilities
     */

    // App store URLs
    getAppStoreUrls() {
        return {
            ios: 'https://apps.apple.com/app/promz/idXXXXXXXXXX',
            android: 'https://play.google.com/apps/testing/ai.promz',
            windows: 'https://www.microsoft.com/store/apps/XXXXXXXXX'
        };
    }

    // Generate app deep link
    generateAppLink(shortId, platform = null) {
        const detectedPlatform = platform || this.getPlatform();

        switch (detectedPlatform) {
            case 'android':
                return `intent://p/${shortId}#Intent;scheme=promz;package=ai.promz;end`;
            case 'ios':
                return `promz://${shortId}`;
            case 'windows':
                return this.getAppStoreUrls().windows;
            default:
                return `/download.html?id=${shortId}`;
        }
    }

    // Try to open Android app with multiple methods
    tryOpenAndroidApp(shortId) {
        this.debugLog('Trying Android intent URL method');

        // Intent format has better compatibility with Chrome on Android
        const intentUrl = `intent://p/${shortId}#Intent;scheme=promz;package=ai.promz;end`;
        this.debugLog(`Using intent URL: ${intentUrl}`);
        window.location.href = intentUrl;

        // Also try the custom URL scheme as a fallback after a short delay
        this.safeTimeout(() => {
            if (!this.appOpened) {
                const appUrl = `promz://p/${shortId}`;
                this.debugLog(`Intent URL might have failed, trying custom scheme: ${appUrl}`);
                window.location.href = appUrl;
            }
        }, 500);
    }

    // Handle deep link functionality
    handleDeepLink() {
        const shortId = this.getShortId();
        if (!shortId) {
            this.debugLog('No shortId found, nothing to do');
            return;
        }

        const platform = this.getPlatform();
        const isMobile = this.isMobileDevice();

        this.debugLog(`Handling deep link with ID: ${shortId}, platform: ${platform}, mobile: ${isMobile}`);

        // Try to open the app first
        if (isMobile) {
            if (platform === 'android') {
                this.tryOpenAndroidApp(shortId);
            } else if (platform === 'ios') {
                this.debugLog('Trying iOS custom URL scheme');
                window.location.href = `promz://${shortId}`;
            }

            // Set a timeout to redirect to app store if app doesn't open
            this.safeTimeout(() => {
                if (this.appOpened) {
                    this.debugLog("App appears to have opened, not redirecting to store");
                    return;
                }

                this.debugLog(`App not opened, redirecting to ${platform} store`);
                const storeUrls = this.getAppStoreUrls();
                if (platform === 'ios') {
                    window.location.href = storeUrls.ios;
                } else if (platform === 'android') {
                    window.location.href = storeUrls.android;
                } else {
                    this.redirectToDownloadPage(shortId);
                }
            }, 2500);
        } else {
            // Desktop users - show download page
            this.debugLog('Desktop user, redirecting to download page');
            this.redirectToDownloadPage(shortId);
        }
    }

    // Redirect to download page with the ID as a parameter
    redirectToDownloadPage(shortId) {
        const downloadUrl = `/download.html?id=${shortId}`;
        this.debugLog(`Redirecting to download page: ${downloadUrl}`);
        window.location.href = downloadUrl;
    }

    // Track app opening state
    setupAppOpenTracking() {
        this.appOpened = false;
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.appOpened = true;
                this.debugLog('App appears to have opened - page hidden');
            }
        });
    }

    // Handle app redirect with fallback (legacy method - for backward compatibility)
    redirectToApp(shortId, fallbackDelay = 2000) {
        // Use the new deep link handler but maintain compatibility
        if (shortId) {
            // Set the shortId in URL for handleDeepLink to use
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('id', shortId);
            window.history.replaceState({}, '', currentUrl);
        }
        this.handleDeepLink();
    }

    /**
     * URL and Path Utilities
     */

    // Parse URL parameters
    getUrlParams() {
        return new URLSearchParams(window.location.search);
    }

    // Get specific URL parameter
    getUrlParam(name) {
        return this.getUrlParams().get(name);
    }

    // Extract ID from path (supports /p/ID format)
    getIdFromPath() {
        const path = window.location.pathname;

        // Check for /p/ prefix format
        if (path.startsWith('/p/')) {
            return path.substring(3);
        }

        // Fallback to direct ID format
        const id = path.startsWith('/') ? path.substring(1) : path;
        return id || null;
    }

    // Get short ID from URL or path
    getShortId() {
        // First try URL parameter
        let shortId = this.getUrlParam('id');

        // Then try path format
        if (!shortId) {
            const pathParts = window.location.pathname.split('/');
            if (pathParts.length >= 3 && pathParts[1] === 'p') {
                shortId = pathParts[2];
            }
        }

        return shortId;
    }

    /**
     * Device Detection Utilities
     */

    // Detect if user is on mobile device
    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // Detect platform
    getPlatform() {
        const userAgent = navigator.userAgent;
        if (/iPad|iPhone|iPod/.test(userAgent)) return 'ios';
        if (/Android/.test(userAgent)) return 'android';
        if (/Windows/.test(userAgent)) return 'windows';
        if (/Mac/.test(userAgent)) return 'mac';
        if (/Linux/.test(userAgent)) return 'linux';
        return 'unknown';
    }

    // Check if Android device
    isAndroid() {
        return /Android/i.test(navigator.userAgent);
    }

    // Check if iOS device
    isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    /**
     * Initialize common functionality
     */
    init() {
        // Load header and footer for all pages
        this.loadComponent('header-placeholder', '/includes/header.html', (link) => {
            return link.href.includes(window.location.pathname.split('/').pop() || 'index.html');
        });

        this.loadComponent('footer-placeholder', '/includes/footer.html');

        // Set up global error handler
        window.addEventListener('error', (event) => {
            this.handleError(event.error, 'Global error handler');
        });

        // Set up unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'Unhandled promise rejection');
        });

        // Setup app open tracking for deep links
        this.setupAppOpenTracking();

        this.debugLog('PromzUtils initialized');
    }
}

// Create global instance
window.PromzUtils = new PromzUtils();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => window.PromzUtils.init());
} else {
    window.PromzUtils.init();
}
